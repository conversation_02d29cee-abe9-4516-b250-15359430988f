<?php

namespace App\Livewire\Dashboard;

use App\Enums\DiagramType;
use App\Model\Dashboards\Dashboard as DashboardModel;
use App\Traits\LivewireGeneralFunctions;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules\Enum;
use Livewire\Component;
use Livewire\WithFileUploads;

class Dashboard extends Component
{
    use LivewireGeneralFunctions;
    use WithFileUploads;

    public ?DashboardModel $dashboard = null;
    public string $path = '';
    public array $dashboardData = [];
    public bool $isInitialLoad = true;
    public $pools = [];
    public $importFile;

    public int $dashboardStatus;

    protected $listeners = [
        'jsSubmitNewWidgetWithLayout' => 'addWidget',
        'jsTriggerRemoveWidgetWithLayout' => 'removeWidget',
        'jsTriggerUpdateWidget' => 'updateWidget',
        'persistClientLayout' => 'persistLayout',
        'reloadWidgetSettings' => 'reloadWidgetSettings',
        'exportDashboard' => 'exportDashboard',
        'importDashboard' => 'importDashboard',
    ];

    public function hydrate(): void
    {
        if ($this->dashboard && $this->dashboard->exists) {
            $this->dashboardStatus = (int) $this->dashboard->active;
        } else {
            $this->dashboardStatus = 0;
        }
    }

    //region Validation Rules
    protected function rulesForNewWidgetData(): array
    {
        return [
            'newWidgetData.title' => 'required|string|max:100',
            'newWidgetData.diagram_type' => ['required','string',new Enum(DiagramType::class)],
            'newWidgetData.pool_type' => 'required|integer|exists:pools,id',
            'newWidgetData.width' => 'required|integer|min:1|max:12',
            'newWidgetData.height' => 'required|integer|min:1|max:10',
            'newWidgetData.menu_link' => 'nullable|url|max:255',
        ];
    }

    protected function messagesForNewWidgetData(): array
    {
        return [
            'newWidgetData.title.required' => __('designer.widget_title_required'),
            'newWidgetData.diagram_type.required' => __('designer.diagram_type_required'),
            'newWidgetData.pool_type.required' => __('designer.pool_type_required'),
        ];
    }

    protected function rulesForUpdateWidgetData(): array
    {
        return [
            'updatedWidgetData.id' => 'required|string',
            'updatedWidgetData.title' => 'required|string|max:100',
            'updatedWidgetData.diagram_type' => ['required', 'string', new Enum(DiagramType::class)],
            'updatedWidgetData.pool_type' => 'required|integer|exists:pools,id',
        ];
    }

    protected function messagesForUpdateWidgetData(): array
    {
        return [
            'updatedWidgetData.title.required' => __('designer.widget_title_required'),
            'updatedWidgetData.diagram_type.required' => __('designer.diagram_type_required'),
            'updatedWidgetData.pool_type.required' => __('designer.pool_type_required'),
        ];
    }
    //endregion

    public function mount(string $path): void
    {
        $this->path = $path;
        $this->pools = DB::table('pools')->select('pool_name', 'id')->get();

        $foundDashboard = DashboardModel::where('url', $this->path)->first();

        if (!$foundDashboard) {
            $newDashboard = new DashboardModel([
                'url' => $this->path,
                'user_id' => auth()->id(),
                'dashboard_data' => [],
                'active' => 0, // Default to inactive (0)
            ]);
            try {
                $newDashboard->save();
                $this->dashboard = $newDashboard;
                $this->showToastr('success', __('designer.dashboard_created'), __('designer.dashboard_created_message'));
            } catch (\Exception $e) {
                Log::error("Error creating dashboard '{$this->path}': {$e->getMessage()}");
                $this->showToastr('error', __('designer.creation_failed'), __('designer.creation_failed_message'));
            }
        } else {
            $this->dashboard = $foundDashboard;
        }

        $this->dashboardStatus = $this->dashboard ? (int) $this->dashboard->active : 0;

        $this->dashboardData = $this->dashboard ? ($this->dashboard->dashboard_data ?? []) : [];
        $this->isInitialLoad = false;
    }

    public function addWidget(array $payload): void
    {
        $newWidgetDataFromJs = $payload['newWidgetData'] ?? [];
        $currentClientLayout = $payload['currentLayout'] ?? [];

        $validator = Validator::make(['newWidgetData' => $newWidgetDataFromJs], $this->rulesForNewWidgetData(), $this->messagesForNewWidgetData());

        if ($validator->fails()) {
            $errors = $validator->errors();
            $this->dispatch('js:validationErrors', ['errors' => $errors->toArray()]);
            $errorMessages = [];
            foreach ($errors->all() as $error) { $errorMessages[] = $error; }
            $this->showToastr('error', __('designer.validation_failed'), implode(' ', $errorMessages));
            return;
        }

        $validatedNewWidgetData = $validator->validated()['newWidgetData'];

        $newWidget = [
            'id' => Str::uuid()->toString(),
            'title' => $validatedNewWidgetData['title'],
            'diagram_type' => $validatedNewWidgetData['diagram_type'],
            'pool_type' => $validatedNewWidgetData['pool_type'],
            'width' => (int)$validatedNewWidgetData['width'],
            'height' => (int)$validatedNewWidgetData['height'],
            'x' => 0,
            'y' => empty($currentClientLayout) ? 0 : collect($currentClientLayout)->max(fn($w) => ($w['y'] ?? 0) + ($w['h'] ?? 0)) ?? 0,
            'settings' => [
                'menu_link' => $validatedNewWidgetData['menu_link'] ?? null,
            ],
        ];

        $tempDashboardData = [];
        if (!empty($currentClientLayout)) {
            foreach ($currentClientLayout as $clientWidget) {
                $tempDashboardData[] = [
                    'id' => $clientWidget['id'],
                    'title' => $clientWidget['title'] ?? __('designer.unknown_title'),
                    'diagram_type' => $clientWidget['diagram_type'] ?? 'Bar',
                    'pool_type' => $clientWidget['pool_type'] ?? 'Dashboard 1',
                    'x' => $clientWidget['x'] ?? 0,
                    'y' => $clientWidget['y'] ?? 0,
                    'width' => $clientWidget['w'] ?? 4,
                    'height' => $clientWidget['h'] ?? 2,
                    'settings' => $clientWidget['settings'] ?? [],
                ];
            }
        }
        $tempDashboardData[] = $newWidget;
        $this->dashboardData = $tempDashboardData;

        $this->dispatch('widgetAddedUnsaved');
        $this->dispatch('gridstack:init');
        $this->showToastr('info', __('designer.widget_added'), __('designer.widget_added_message'));
    }

    public function removeWidget(array $payload): void
    {
        $widgetIdToRemove = $payload['widgetId'] ?? null;
        $currentClientLayout = $payload['currentLayout'] ?? [];

        if (!$widgetIdToRemove) {
            Log::warning('removeWidget called without widgetId.');
            return;
        }

        $widgetToRemoveTitle = __('designer.selected_widget');
        $newLayout = [];
        foreach ($currentClientLayout as $widget) {
            if ($widget['id'] === $widgetIdToRemove) {
                $widgetToRemoveTitle = $widget['title'] ?? $widgetToRemoveTitle;
                continue;
            }
            $newLayout[] = [
                'id' => $widget['id'],
                'title' => $widget['title'] ?? __('designer.unknown_title'),
                'diagram_type' => $widget['diagram_type'] ?? 'Bar',
                'pool_type' => $widget['pool_type'] ?? 'Dashboard 1',
                'x' => $widget['x'] ?? 0,
                'y' => $widget['y'] ?? 0,
                'width' => $widget['w'] ?? 4,
                'height' => $widget['h'] ?? 2,
                'settings' => $widget['settings'] ?? [],
            ];
        }
        $this->dashboardData = $newLayout;

        $this->showToastr('info', __('designer.widget_removed'), __('designer.widget_removed_message', ['title' => $widgetToRemoveTitle]));
        $this->dispatch('widgetRemovedUnsaved');
        $this->dispatch('gridstack:init');
    }

    public function updateWidget(array $payload): void
    {
        $updatedWidgetData = $payload['updatedWidgetData'] ?? [];
        $currentClientLayout = $payload['currentLayout'] ?? [];

        $validator = Validator::make(['updatedWidgetData' => $updatedWidgetData], $this->rulesForUpdateWidgetData(), $this->messagesForUpdateWidgetData());

        if ($validator->fails()) {
            $errors = $validator->errors();
            $this->dispatch('js:validationErrors', ['errors' => $errors->toArray()]);
            $errorMessages = [];
            foreach ($errors->all() as $error) { $errorMessages[] = $error; }
            $this->showToastr('error', __('designer.validation_failed'), implode(' ', $errorMessages));
            return;
        }

        $validatedData = $validator->validated()['updatedWidgetData'];
        $widgetIdToUpdate = $validatedData['id'];

        $this->dashboardData = collect($currentClientLayout)->map(function ($widget) use ($widgetIdToUpdate, $validatedData) {
            $newWidget = [
                'id' => $widget['id'],
                'title' => $widget['title'] ?? __('designer.unknown'),
                'diagram_type' => $widget['diagram_type'] ?? 'Bar',
                'pool_type' => $widget['pool_type'] ?? 'Dashboard 1',
                'x' => $widget['x'] ?? 0,
                'y' => $widget['y'] ?? 0,
                'width' => $widget['w'] ?? 4,
                'height' => $widget['h'] ?? 2,
                'settings' => $widget['settings'] ?? [],
            ];

            if ($widget['id'] === $widgetIdToUpdate) {
                $newWidget['title'] = $validatedData['title'];
                $newWidget['diagram_type'] = $validatedData['diagram_type'];
                $newWidget['pool_type'] = $validatedData['pool_type'];
            }

            return $newWidget;
        })->toArray();

        $this->showToastr('info', __('designer.widget_updated'), __('designer.widget_updated_message', ['title' => $validatedData['title']]));
        $this->dispatch('widgetUpdatedUnsaved');
        $this->dispatch('gridstack:init');
    }

    /**
     * Reloads the settings for a specific widget from the database while preserving its current layout (x, y, width, height).
     * @param array $payload Contains 'widgetId' and 'currentLayout'.
     */
    public function reloadWidgetSettings(array $payload): void
    {
        $widgetIdToReload = $payload['widgetId'] ?? null;
        $currentClientLayout = $payload['currentLayout'] ?? [];

        if (!$widgetIdToReload || !$this->dashboard) {
            Log::warning('reloadWidgetSettings called without widgetId or dashboard model.');
            $this->showToastr('error', __('designer.error'), __('designer.reload_error'));
            return;
        }

        try {
            $latestDashboard = DashboardModel::find($this->dashboard->id);
            $dbDashboardData = $latestDashboard->dashboard_data ?? [];

            $reloadedTitle = __('designer.unknown_widget');

            $tempDashboardData = [];

            foreach ($currentClientLayout as $clientWidget) {
                if ($clientWidget['id'] === $widgetIdToReload) {
                    $dbWidget = collect($dbDashboardData)->firstWhere('id', $widgetIdToReload);

                    if ($dbWidget) {
                        $tempDashboardData[] = [
                            'id' => $dbWidget['id'],
                            'title' => $dbWidget['title'] ?? $clientWidget['title'],
                            'diagram_type' => $dbWidget['diagram_type'] ?? $clientWidget['diagram_type'],
                            'pool_type' => $dbWidget['pool_type'] ?? $clientWidget['pool_type'],
                            'x' => $clientWidget['x'] ?? 0,
                            'y' => $clientWidget['y'] ?? 0,
                            'width' => $clientWidget['w'] ?? 4,
                            'height' => $clientWidget['h'] ?? 2,
                            'settings' => $dbWidget['settings'] ?? [],
                        ];
                        $reloadedTitle = $dbWidget['title'] ?? $reloadedTitle;
                    } else {
                        $tempDashboardData[] = [
                            'id' => $clientWidget['id'],
                            'title' => $clientWidget['title'] ?? __('designer.unknown_title'),
                            'diagram_type' => $clientWidget['diagram_type'] ?? 'Bar',
                            'pool_type' => $clientWidget['pool_type'] ?? 'Dashboard 1',
                            'x' => $clientWidget['x'] ?? 0,
                            'y' => $clientWidget['y'] ?? 0,
                            'width' => $clientWidget['w'] ?? 4,
                            'height' => $clientWidget['h'] ?? 2,
                            'settings' => $clientWidget['settings'] ?? [],
                        ];
                    }
                } else {
                    $tempDashboardData[] = [
                        'id' => $clientWidget['id'],
                        'title' => $clientWidget['title'] ?? __('designer.unknown_title'),
                        'diagram_type' => $clientWidget['diagram_type'] ?? 'Bar',
                        'pool_type' => $clientWidget['pool_type'] ?? 'Dashboard 1',
                        'x' => $clientWidget['x'] ?? 0,
                        'y' => $clientWidget['y'] ?? 0,
                        'width' => $clientWidget['w'] ?? 4,
                        'height' => $clientWidget['h'] ?? 2,
                        'settings' => $clientWidget['settings'] ?? [],
                    ];
                }
            }

            $this->dashboardData = $tempDashboardData;

            $this->dispatch('gridstack:init');
            $this->showToastr('success', __('designer.settings_reloaded'), __('designer.settings_reloaded_message', ['title' => $reloadedTitle]));

        } catch (\Exception $e) {
            Log::error("Error reloading widget settings for '{$widgetIdToReload}' on dashboard '{$this->path}': {$e->getMessage()}");
            $this->showToastr('error', __('designer.reload_failed'), __('designer.reload_failed_message'));
        }
    }

    /**
     * Livewire lifecycle hook: Called when the 'dashboardStatus' property is updated via wire:model.live.
     */
    public function updatedDashboardStatus($value): void
    {
        if (!isset($value) || !in_array($value, [0, 1, 2])) {
            Log::warning('updatedDashboardStatus called with invalid status value.', ['value' => $value]);
            $this->showToastr('error', __('designer.error'), __('designer.invalid_status'));
            return;
        }

        if (!$this->dashboard || !$this->dashboard->exists) {
            $this->showToastr('error', __('designer.error'), __('designer.dashboard_not_found_for_update'));
            Log::error('Attempted to update status on a non-existent or unsaved dashboard model. URL: ' . $this->path);
            $this->dashboardStatus = 0; // Revert to default
            return;
        }

        try {
            $this->dashboard->active = $value;
            $this->dashboard->save();

            $statusText = '';
            switch ($value) {
                case 1:
                    $statusText = __('designer.activated');
                    break;
                case 2:
                    $statusText = __('designer.partly_activated');
                    break;
                case 0:
                default:
                    $statusText = __('designer.deactivated');
                    break;
            }

            $this->dispatch('gridstack:init');
            $this->showToastr('success', __('designer.status_updated'), __('designer.dashboard_status_message', ['status' => $statusText]));
        } catch (\Exception $e) {
            Log::error("Error updating dashboard activity status for '{$this->path}': {$e->getMessage()}");
            $this->showToastr('error', __('designer.update_failed'), __('designer.status_update_failed'));
            $this->dashboardStatus = (int) $this->dashboard->getOriginal('active');
        }
    }

    public function persistLayout(array $payload): void
    {
        $clientLayout = $payload['clientLayout'] ?? [];
        Log::debug("Livewire: persistLayout received", ['layout_count' => count($clientLayout)]);

        $this->dashboardData = collect($clientLayout)->map(function ($item) {
            return [
                'id' => $item['id'],
                'title' => $item['title'] ?? 'Widget',
                'diagram_type' => $item['diagram_type'] ?? 'Bar',
                'pool_type' => $item['pool_type'] ?? 'Dashboard 1',
                'x' => $item['x'] ?? 0,
                'y' => $item['y'] ?? 0,
                'width' => $item['w'] ?? 4,
                'height' => $item['h'] ?? 2,
                'settings' => $item['settings'] ?? [],
            ];
        })->toArray();

        $this->saveToDatabase();
        $this->dispatch('gridstack:init');
    }

    public function triggerSaveLayoutToDb(): void
    {
        $this->dispatch('js:sendLayoutForSaving');
    }

    protected function saveToDatabase(): void
    {
        if (!$this->dashboard || !$this->dashboard->exists) {
            Log::error('Attempted to save data but dashboard model is not loaded or does not exist in DB.');
            $this->showToastr('error', __('designer.save_error'), __('designer.dashboard_instance_not_found'));
            return;
        }
        try {
            $this->dashboard->dashboard_data = $this->dashboardData;
            $this->dashboard->save();
            $this->showToastr('success', __('designer.layout_saved'), __('designer.layout_saved_message'));
            $this->dispatch('layoutSaved');
        } catch (\Exception $e) {
            Log::error("Error saving dashboard data for '{$this->path}' to DB: {$e->getMessage()}");
            $this->showToastr('error', __('designer.save_failed'), __('designer.save_failed_message'));
        }
    }

    /**
     * Export the dashboard configuration as JSON
     */
    public function exportDashboard(array $payload = []): void
    {
        $currentLayout = $payload['currentLayout'] ?? [];

        if (!$this->dashboard || !$this->dashboard->exists) {
            $this->showToastr('error', __('designer.error'), __('designer.dashboard_not_found'));
            return;
        }

        try {
            $dataToExport = !empty($currentLayout) ? $currentLayout : $this->dashboardData;

            $exportData = [
                'version' => '1.0',
                'dashboard_name' => $this->dashboard->url ?? 'dashboard',
                'exported_at' => now()->toIso8601String(),
                'widgets' => collect($dataToExport)->map(function ($widget) {
                    if($image = $widget['settings']['image']) {
                        $image = Storage::url('images/' . $image);
                        //use base64 encoded image even if it is png jpg or jpeg
                        if (in_array(pathinfo($image, PATHINFO_EXTENSION), ['png', 'jpg', 'jpeg'])) {
                            $image = 'data:image/' . pathinfo($image, PATHINFO_EXTENSION) . ';base64,' . base64_encode(file_get_contents(public_path($image)));
                        }

                        $widget['settings']['image'] = $image;
                    }
                    return [
                        'id' => $widget['id'] ?? Str::uuid()->toString(),
                        'title' => $widget['title'],
                        'diagram_type' => $widget['diagram_type'],
                        'pool_type' => $widget['pool_type'],
                        'x' => $widget['x'] ?? 0,
                        'y' => $widget['y'] ?? 0,
                        'width' => $widget['width'] ?? $widget['w'] ?? 4,
                        'height' => $widget['height'] ?? $widget['h'] ?? 2,
                        'settings' => $widget['settings'] ?? [],
                    ];
                })->values()->toArray(),
                'pools_mapping' => $this->pools->map(function ($pool) {
                    return [
                        'id' => $pool->id,
                        'name' => $pool->pool_name,
                    ];
                })->toArray(),
            ];

            $jsonContent = json_encode($exportData, JSON_PRETTY_PRINT);
            $fileName = 'dashboard-' . $this->dashboard->url . '-' . date('Y-m-d-His') . '.json';

            $this->dispatch('downloadDashboardExport', [
                'content' => $jsonContent,
                'filename' => $fileName,
            ]);

            $this->dispatch('gridstack:init');
            $this->showToastr('success', __('designer.export_successful'), __('designer.export_successful_message'));
        } catch (\Exception $e) {
            Log::error("Error exporting dashboard: {$e->getMessage()}");
            $this->showToastr('error', __('designer.export_failed'), __('designer.export_failed_message'));
        }
    }

    /**
     * Import dashboard configuration from uploaded JSON file
     */
    public function importDashboard($fileContent = null): void
    {
        try {
            if ($fileContent) {
                $importData = json_decode($fileContent, true);
            } else {
                $this->validate([
                    'importFile' => 'required|file|mimes:json|max:2048',
                ]);
                $content = file_get_contents($this->importFile->getRealPath());
                $importData = json_decode($content, true);
            }

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception(__('designer.invalid_json_file'));
            }

            if (!isset($importData['version']) || !isset($importData['widgets']) || !is_array($importData['widgets'])) {
                throw new \Exception(__('designer.invalid_dashboard_format'));
            }

            $importedWidgets = [];
            foreach ($importData['widgets'] as $widget) {
                if (!isset($widget['title']) || !isset($widget['diagram_type'])) {
                    continue;
                }

                $poolId = $widget['pool_type'];
                if (isset($importData['pools_mapping']) && is_array($importData['pools_mapping'])) {
                    $poolName = collect($importData['pools_mapping'])->firstWhere('id', $poolId)['name'] ?? null;
                    if ($poolName) {
                        $localPool = $this->pools->firstWhere('pool_name', $poolName);
                        if ($localPool) {
                            $poolId = $localPool->id;
                        }
                    }
                }

                if (!$this->pools->contains('id', $poolId)) {
                    $poolId = $this->pools->first()->id ?? 1;
                }

                if($widget['settings']['image']) {
                    //check if base64 encoded image and save if it is
                    if (preg_match('/^data:image\/(\w+);base64,/', $widget['settings']['image'], $type)) {
                        $data = substr($widget['settings']['image'], strpos($widget['settings']['image'], ',') + 1);
                        $data = base64_decode($data);
                        if ($data === false) {
                            throw new \Exception(__('designer.invalid_base64_image'));
                        }

                        $extension = strtolower($type[1]); // e.g., jpg, png
                        $fileName = Str::uuid()->toString() . '.' . $extension;
                        //@TODO refactor for s3
                        $filePath = 'public/images/widgets/' . $fileName;

                        // This uses the default disk, which can be local or s3 based on config
                        Storage::put($filePath, $data);

                        //get public path after the url
                        $widget['settings']['image'] = 'widgets/' . $fileName; // Store relative path
                    }
                }

                $importedWidgets[] = [
                    'id' => Str::uuid()->toString(),
                    'title' => $widget['title'],
                    'diagram_type' => $widget['diagram_type'],
                    'pool_type' => $poolId,
                    'x' => $widget['x'] ?? 0,
                    'y' => $widget['y'] ?? 0,
                    'width' => $widget['width'] ?? 4,
                    'height' => $widget['height'] ?? 2,
                    'settings' => $widget['settings'] ?? [],
                ];
            }

            if (empty($importedWidgets)) {
                throw new \Exception(__('designer.no_valid_widgets_found'));
            }

            $this->dashboardData = $importedWidgets;

            if ($this->dashboard && $this->dashboard->exists) {
                $this->dashboard->dashboard_data = $this->dashboardData;
                $this->dashboard->save();
                $this->showToastr('success', __('designer.import_successful'), __('designer.imported_layout_saved_message'));
            } else {
                $this->showToastr('info', __('designer.note'), __('designer.imported_layout_unsaved'));
            }

            $this->dispatch('widgetAddedUnsaved');
            $this->dispatch('gridstack:init');
            $this->dispatch('importSuccess');
            $this->showToastr('success', __('designer.import_successful'), __('designer.import_successful_message'));

            $this->reset('importFile');
        } catch (\Exception $e) {
            Log::error("Error importing dashboard: {$e->getMessage()}");
            $this->showToastr('error', __('designer.import_failed'), $e->getMessage());
            $this->reset('importFile');
        }
    }

    public function render()
    {
        return view('livewire.dashboard.dashboard');
    }
}