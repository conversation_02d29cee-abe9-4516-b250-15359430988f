<div>
    <canvas id="{{$uniqueId}}_widget_chart"></canvas>
</div>

@assets
<script src="{{ asset('/js/chart/chart.min.js') }}"></script>
<script src="{{ asset('/js/chart/chartjs-plugin-annotation.min.js') }}"></script>
@endassets

@script
<script>
    let id = '{{$uniqueId}}';
    let filter = @js($filter);
    let results = @json($results);
    let originalLabels = @json($labels); // Full labels array
    let chartType = 'radar';

    const MAX_LABEL_LENGTH = 12; // Customize truncation length here

    // Truncate label visually for chart pointLabels
    function truncateLabel(label, maxLength = MAX_LABEL_LENGTH) {
        return label.length > maxLength ? label.substring(0, maxLength - 3) + '...' : label;
    }

    // Create truncated labels for visual use only
    const truncatedLabels = originalLabels.map(label => truncateLabel(label));

    function createRadarChart(id, labels, datasets, filters) {
        const canvasId = `${id}_widget_chart`;
        const ctx = document.getElementById(canvasId).getContext('2d');

        const options = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { display: false, position: 'top' },
                tooltip: {
                callbacks: {
                    title: () => '',  // Hide default title line
                    label: function(context) {
                        const index = context.dataIndex;
                        const fullLabel = originalLabels[index] || '';
                        const value = context.dataset.data[index];
                        return filters ? `${fullLabel}: ${value}` : `${fullLabel}`;
                    }
                },
                bodyFont: { size: 14 },
                padding: 10,
            },

                zoom: {
                    pan: { enabled: true, mode: 'xy' },
                    zoom: { enabled: true, mode: 'xy' },
                },
            },
            scales: {
                r: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 10,
                        reverse: false
                    },
                    pointLabels: {
                        display: true,
                        centerPointLabels: false,
                        font: { size: 16 },
                        callback: function(value, index) {
                            return truncatedLabels[index] || value;
                        }
                    }
                }
            }
        };

        const data = {
            labels: originalLabels, // Pass original labels for tooltip indexing
            datasets: getFormattedDatasets(datasets, filters),
        };

        if (window.chartInstances && window.chartInstances[canvasId]) {
            window.chartInstances[canvasId].destroy();
        }

        window.chartInstances = {
            ...(window.chartInstances || {}),
            [canvasId]: new Chart(ctx, { type: 'radar', data, options })
        };
    }

    function getFormattedDatasets(datasets, filters) {
        if (!filters) {
            const chartColors = getChartColors(datasets);
            return [{
                data: datasets,
                fill: true,
                backgroundColor: 'transparent',
                pointBackgroundColor: chartColors.backgroundColor,
                pointBorderColor: chartColors.borderColor,
                pointHoverBackgroundColor: chartColors.borderColor,
                pointHoverBorderColor: chartColors.backgroundColor,
                pointRadius: 5,
            }];
        }

        return datasets.map((data, index) => ({
            data: data,
            backgroundColor: index === 0 ? 'rgba(255, 99, 132, 0.2)' : 'rgba(75, 192, 192, 0.2)',
            borderColor: index === 0 ? 'rgba(255, 99, 132, 1)' : 'rgba(47, 171, 102, 1)',
            borderWidth: 1,
        }));
    }

    function getChartColors(dataSet) {
        if (!dataSet || !Array.isArray(dataSet)) {
            return { backgroundColor: [], borderColor: [] };
        }

        return dataSet.reduce((colors, value) => {
            let bgColor, borderColor;
            if (value <= 10) {
                [bgColor, borderColor] = ['rgba(232, 78, 27, 1)', 'rgb(232, 78, 27)'];
            } else if (value <= 69) {
                [bgColor, borderColor] = ['rgba(248, 177, 51, 1)', 'rgb(248, 177, 51)'];
            } else if (value <= 100) {
                [bgColor, borderColor] = ['rgba(47, 171, 102, 1)', 'rgb(47, 171, 102)'];
            } else {
                [bgColor, borderColor] = ['rgba(0, 0, 0, 0.2)', 'rgb(0, 0, 0)'];
            }

            colors.backgroundColor.push(bgColor);
            colors.borderColor.push(borderColor);
            return colors;
        }, { backgroundColor: [], borderColor: [] });
    }

    // Initial chart creation
    createRadarChart(id, originalLabels, results, filter);
</script>
@endscript