<?php

return [
    // Dashboard Management
    'back_to_dashboard' => 'Back to Dashboard',
    'dashboard_created' => 'Dashboard Created',
    'dashboard_created_message' => 'New dashboard initialized successfully!',
    'creation_failed' => 'Creation Failed',
    'creation_failed_message' => 'Could not create the new dashboard.',
    'dashboard_not_found' => 'Dashboard not found.',
    'dashboard_instance_not_found' => 'Dashboard instance not found.',

    // Widget Management
    'add_widget' => 'Add Widget',
    'add_new_widget' => 'Add New Widget',
    'title' => 'Title',
    'diagram_type' => 'Diagram Type',
    'select_type' => 'Select a type',
    'data_pool' => 'Data Pool',
    'select_pool' => 'Select a Pool',
    'width_cols' => 'Width (Cols)',
    'height_rows' => 'Height (Rows)',
    'menu_link' => 'Menu Link',
    'menu_link_help' => 'Optional link that will make the widget title clickable',
    'cancel' => 'Cancel',

    // Widget Messages
    'widget_added' => 'Widget Added',
    'widget_added_message' => 'Widget added to layout. Click "Save Layout to DB" to persist.',
    'widget_removed' => 'Widget Removed',
    'widget_removed_message' => ':title removed. Click "Save Layout to DB" to persist.',
    'widget_updated' => 'Widget Updated',
    'widget_updated_message' => ':title updated. Save layout to persist changes.',

    // Validation Messages
    'validation_failed' => 'Validation Failed',
    'widget_title_required' => 'The widget title is essential.',
    'diagram_type_required' => 'Please select a diagram type.',
    'pool_type_required' => 'Please select a data pool.',

    // Widget Settings
    'edit_widget' => 'Edit Widget',
    'reload_settings' => 'Reload Widget Settings',
    'delete_widget' => 'Delete Widget',
    'settings_reloaded' => 'Settings Reloaded',
    'settings_reloaded_message' => ':title settings have been reloaded from DB!',
    'reload_failed' => 'Reload Failed',
    'reload_failed_message' => 'Could not reload widget settings.',
    'reload_error' => 'Cannot reload widget settings.',

    // Layout Management
    'save_layout_to_db' => 'Save Layout to DB',
    'saving_to_db' => 'Saving to DB...',
    'layout_saved' => 'Layout Saved',
    'layout_saved_message' => 'Dashboard layout has been saved to the database!',
    'save_error' => 'Save Error',
    'save_failed' => 'Save Failed',
    'save_failed_message' => 'Could not save dashboard layout to the database.',

    // Dashboard Status
    'activate' => 'Activate',
    'deactivate' => 'Deactivate',
    'activated' => 'activated',
    'deactivated' => 'deactivated',
    'status_active' => 'Status: Active',
    'status_inactive' => 'Status: Inactive',
    'status_updated' => 'Status Updated',
    'status_updated_message' => 'Dashboard successfully :status!',
    'update_failed' => 'Update Failed',
    'status_update_failed' => 'Could not update dashboard status.',

    // Import/Export
    'export' => 'Export',
    'import' => 'Import',
    'export_dashboard_title' => 'Export dashboard configuration',
    'import_dashboard_title' => 'Import dashboard configuration',
    'import_dashboard_config' => 'Import Dashboard Configuration',
    'select_json_file' => 'Select JSON File',
    'select_json_help' => 'Select a previously exported dashboard configuration file (JSON format, max 2MB)',
    'import_configuration' => 'Import Configuration',
    'importing' => 'Importing...',
    'export_successful' => 'Export Successful',
    'export_successful_message' => 'Dashboard configuration exported successfully!',
    'export_failed' => 'Export Failed',
    'export_failed_message' => 'Could not export dashboard configuration.',
    'import_successful' => 'Import Successful',
    'import_successful_message' => 'Dashboard configuration imported successfully! Remember to save the layout.',
    'import_failed' => 'Import Failed',
    'invalid_json_file' => 'Invalid JSON file',
    'invalid_dashboard_format' => 'Invalid dashboard configuration format',
    'no_valid_widgets' => 'No valid widgets found in the import file',

    // Loading Messages
    'saving_layout' => 'Saving layout...',
    'updating_status' => 'Updating status...',
    'importing_dashboard' => 'Importing dashboard...',
    'updating_layout' => 'Updating layout...',
    'removing_widget' => 'Removing widget...',
    'adding_widget' => 'Adding widget...',
    'updating_widget' => 'Updating widget...',
    'reloading_settings' => 'Reloading settings...',

    // Warnings and Errors
    'unsaved_changes' => 'You have unsaved changes.',
    'unsaved_changes_warning' => 'You have unsaved changes. Are you sure you want to leave?',
    'error' => 'Error',
    'dashboard_empty' => 'Dashboard is Empty',
    'dashboard_empty_message' => 'Click the "Add Widget" button above to get started.',

    // Widget Meta Info
    'type' => 'Type',
    'pool' => 'Pool',
    'unknown' => 'Unknown',
    'unknown_title' => 'Unknown Title',
    'unknown_widget' => 'Unknown Widget',

    // Confirmations
    'confirm_delete_widget' => 'Are you sure you want to delete this widget? This action will remove it from the current layout. Save to make it permanent.',

    // File Operations
    'file_selected' => 'File selected: :filename',
    'please_select_file' => 'Please select a file',
    'please_select_json' => 'Please select a JSON file',
    'file_size_exceeded' => 'File size must be less than 2MB',
    'error_reading_file' => 'Error reading file: :error',

    // JavaScript Error Messages
    'correct_errors' => 'Please correct the following errors:',
    'gridstack_error' => 'Failed to load dashboard grid. Please try refreshing.',
    'no_items_to_initialize' => 'No items to initialize from Blade, grid is empty.',

    // Success Messages
    'operation_successful' => 'Operation completed successfully!',
    'changes_saved' => 'Changes have been saved.',
    'save' => 'Save',

    'status_partly_active' => 'Partly Active',
    'status_not_available' => 'Status Not Available', // For when dashboard is not yet created/persisted
    'assigned_users' => 'Assigned Users', // New button text

    'partly_activated' => 'partly activated', // used in toastr messages

    'dashboard_status_message' => 'Dashboard status updated to :status.', // used in toastr message

    'dashboard_not_found_for_update' => 'Dashboard not found or not yet saved. Cannot update status.', // Backend error
    'invalid_status' => 'Invalid status provided.', // Backend error

    'imported_layout_saved_message' => 'Dashboard layout imported and saved successfully!',
    'imported_layout_unsaved' => 'Dashboard layout imported but not yet saved to database. Click "Save Layout" to persist changes.',

    // General messages (if not already present and needed for clarity based on context)
    'note' => 'Note',
];